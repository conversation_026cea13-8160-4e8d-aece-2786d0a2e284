{"tasks": {"dev": "deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv main.ts", "start": "deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv main.ts", "demo": "deno run --allow-all --unstable-kv demo_server.ts", "test-demo": "deno run --allow-net --allow-env --unstable-kv /tests/test_demo.ts", "verify": "deno run --allow-all verify_migration.ts"}, "imports": {"@std/assert": "jsr:@std/assert@1", "@std/http": "jsr:@std/http@1", "@std/path": "jsr:@std/path@1", "@std/toml": "jsr:@std/toml@1", "@polar-sh/sdk": "npm:@polar-sh/sdk@^0.34.0", "ts-pattern": "https://esm.sh/ts-pattern@5", "openai": "npm:openai@^4.73.1"}}