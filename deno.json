{"tasks": {"dev": "deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv main.ts", "start": "deno run --allow-net --allow-read --allow-write --allow-env --unstable-kv main.ts"}, "imports": {"@std/assert": "jsr:@std/assert@1", "@std/http": "jsr:@std/http@1", "@std/path": "jsr:@std/path@1", "@std/toml": "jsr:@std/toml@1", "stripe": "https://esm.sh/stripe@17", "ts-pattern": "https://esm.sh/ts-pattern@5"}}